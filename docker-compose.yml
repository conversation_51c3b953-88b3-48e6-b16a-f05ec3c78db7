version: '3.8'

# Define default environment variables
x-environment: &default-environment
  NODE_ENV: production
  PORT: 5001
  WEBSOCKET_ENABLED: "true"
  JWT_SECRET: ${JWT_SECRET:-your-secret-key}

# Redis configuration
x-redis-config: &redis-config
  REDIS_ENABLED: "true"
  REDIS_HOST: redis
  REDIS_PORT: 6379
  REDIS_USERNAME: ${REDIS_USERNAME:-default}
  REDIS_PASSWORD: ${REDIS_PASSWORD:-redispassword}
  REDIS_URL: redis://:${REDIS_PASSWORD:-redispassword}@redis:6379
  REDIS_DB: 0
  REDIS_PREFIX: ""

# MongoDB configuration - using external MongoDB
x-mongodb-config: &mongodb-config
  MONGODB_HOST: ${MONGODB_HOST:-'host.docker.internal:27017'}
  MONGODB_PORT: ${MONGODB_PORT:-27017}
  MONGODB_DATABASE: ${MONGODB_DATABASE:-hirafi_test }
  MONGODB_USERNAME: ${MONGODB_USERNAME:-}
  MONGODB_PASSWORD: ${MONGODB_PASSWORD:-}
  MONGODB_USE_SSL: ${MONGODB_USE_SSL:-false}
  MONGODB_AUTH_SOURCE: ${MONGODB_AUTH_SOURCE:-admin}


services:
  test-hub:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: test-hub
    ports:
      - "5001:5000"
    environment:
      <<: [*default-environment, *redis-config, *mongodb-config]
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - test-hub-network

  redis:
    image: redis:latest
    container_name: test-hub-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-redispassword}
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - test-hub-network



volumes:
  redis-data:

networks:
  test-hub-network:
    driver: bridge
