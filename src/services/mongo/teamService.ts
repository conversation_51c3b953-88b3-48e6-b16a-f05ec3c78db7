import { Collection } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger.js';
import { db, ensureMongoDBConnection, isMongoDBInitialized, teamsCollection, usersCollection } from './dbConnection.js';
import { Team, TeamStatus, TeamMember, MembershipStatus } from '../../models/team.js';
import { User, AccountType } from '../../models/user.js';

// Takım oluştur
export async function createTeam(teamData: {
  name: string;
  description?: string;
  companyId: string;
  createdBy: string;
}): Promise<{
  success: boolean;
  teamId?: string;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const teamId = uuidv4();
    const now = new Date();

    const team: Team = {
      id: teamId,
      name: teamData.name,
      description: teamData.description || '',
      companyId: teamData.companyId,
      status: TeamStatus.ACTIVE,
      createdBy: teamData.createdBy,
      createdAt: now,
      updatedAt: now,
      memberCount: 1,
      last_active: now
    };

    // Takımı ekle
    await teamsCollection.insertOne(team);

    // Varsayılan roller artık global olarak role_permissions tablosunda mevcut

    // Takım oluşturanı team_admin olarak ekle
    const rolePermissionsCollection = db.collection('role_permissions');
    
    // Sistem team_admin rolünü bul
    const teamAdminRole = await rolePermissionsCollection.findOne({
      role_id: 'team_admin',
      is_system: true
    });

    if (teamAdminRole) {
      const memberAssignment = {
        id: uuidv4(),
        user_id: teamData.createdBy,
        team_id: teamId,
        company_id: teamData.companyId,
        role_id: 'team_admin',
        role_name: teamAdminRole.role_name,
        permissions: teamAdminRole.permissions,
        status: 'active',
        added_by: teamData.createdBy,
        added_at: now,
        created_at: now,
        is_system: false
      };

      await rolePermissionsCollection.insertOne(memberAssignment);

      // Kullanıcının teamId alanını güncelle
      await usersCollection.updateOne(
        { id: teamData.createdBy },
        { $set: { teamId: teamId } }
      );
    }

    logger.info(`Team created successfully with ID: ${teamId}`);

    return {
      success: true,
      teamId
    };
  } catch (error: any) {
    logger.error(`Error creating team: ${error.message}`);
    return { success: false, message: `Failed to create team: ${error.message}` };
  }
}

// Kullanıcının takımlarını getir
export async function getUserTeams(userId: string): Promise<{
  success: boolean;
  teams?: Team[];
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    // Kullanıcının üye olduğu takımların ID'lerini al
    const memberships = await rolePermissionsCollection
      .find({
        user_id: userId,
        status: 'active',
        is_system: false
      })
      .toArray();

    if (memberships.length === 0) {
      return { success: true, teams: [] };
    }

    const teamIds = memberships.map(m => m.team_id);

    // Takım bilgilerini getir
    const teams = await teamsCollection
      .find({
        id: { $in: teamIds },
        status: TeamStatus.ACTIVE
      })
      .toArray();

    return {
      success: true,
      teams: teams as Team[]
    };
  } catch (error: any) {
    logger.error(`Error getting user teams: ${error.message}`);
    return { success: false, message: 'Failed to get user teams' };
  }
}

// Takım üyesi ekle
export async function addTeamMember(
  teamId: string,
  userId: string,
  roleId: string,
  addedBy: string
): Promise<{
  success: boolean;
  memberId?: string;
  message?: string;
}> {
  if (!teamId || !userId || !roleId) {
    logger.error(`Cannot add team member: required fields missing. teamId=${teamId}, userId=${userId}, roleId=${roleId}`);
    return { success: false, message: 'teamId, userId and roleId are required' };
  }
  
  try {
    if (!isMongoDBInitialized() || !db || !usersCollection || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    logger.info(`Adding team member - teamId: ${teamId}, userId: ${userId}, roleId: ${roleId}, addedBy: ${addedBy}`);

    // Kullanıcı ve takımın var olup olmadığını kontrol et
    const user = await usersCollection.findOne({ id: userId });
    if (!user) {
      logger.error(`User with ID ${userId} not found`);
      return { success: false, message: `User with ID ${userId} not found` };
    }

    const team = await teamsCollection.findOne({ id: teamId });
    if (!team) {
      logger.error(`Team with ID ${teamId} not found`);
      return { success: false, message: `Team with ID ${teamId} not found` };
    }

    // Kullanıcının zaten üye olup olmadığını kontrol et
    const existingMember = await rolePermissionsCollection.findOne({
      team_id: teamId,
      user_id: userId,
      is_system: false
    });

    // Sistem rollerinden rol bilgilerini al
    const systemRole = await rolePermissionsCollection.findOne({
      role_id: roleId,
      is_system: true
    });

    if (!systemRole) {
      logger.error(`System role with ID ${roleId} not found`);
      return { success: false, message: `Role with ID ${roleId} not found` };
    }

    // Mevcut üye varsa güncelle, yoksa yeni oluştur
    if (existingMember) {
      // Mevcut üyeyi güncelle
      await rolePermissionsCollection.updateOne(
        { id: existingMember.id },
        {
          $set: {
            role_id: roleId,
            role_name: systemRole.role_name,
            permissions: systemRole.permissions,
            status: 'active',
            added_by: addedBy,
            updated_at: new Date()
          }
        }
      );

      logger.info(`Updated user ${userId} role to ${roleId} in team ${teamId}`);
      return {
        success: true,
        memberId: existingMember.id,
        message: `User role updated to ${systemRole.role_name}`
      };
    }

    // Yeni üyelik oluştur
    const memberId = uuidv4();
    const now = new Date();

    const newMember = {
      id: memberId,
      user_id: userId,
      team_id: teamId,
      company_id: team.companyId,
      role_id: roleId,
      role_name: systemRole.role_name,
      permissions: systemRole.permissions,
      status: 'active',
      added_by: addedBy,
      added_at: now,
      created_at: now,
      is_system: false
    };

    await rolePermissionsCollection.insertOne(newMember);

    // Kullanıcının teamId alanını güncelle
    await usersCollection.updateOne(
      { id: userId },
      { $set: { teamId: teamId } }
    );

    logger.info(`Added user ${userId} to team ${teamId} with role ${roleId}`);

    return {
      success: true,
      memberId
    };
  } catch (error: any) {
    logger.error(`Error adding team member: ${error.message}`);
    return { success: false, message: `Failed to add team member: ${error.message}` };
  }
}

// Takım üyelerini getir
export async function getTeamMembers(
  teamId: string,
  options: {
    limit?: number;
    skip?: number;
    includeInactive?: boolean;
  } = {}
): Promise<{
  success: boolean;
  members?: any[];
  total?: number;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const { limit = 10, skip = 0, includeInactive = false } = options;
    const rolePermissionsCollection = db.collection('role_permissions');

    // Takım üyelerini filtrele (role_permissions'dan)
    const query: any = {
      team_id: teamId,
      user_id: { $exists: true, $ne: null }, // Sadece user assignment'ları
      is_system: false // Sistem rolleri değil
    };

    // Aktif üyeleri filtrele
    if (!includeInactive) {
      query.status = 'active';
    }

    // Toplam sayıyı al
    const total = await rolePermissionsCollection.countDocuments(query);

    // Üyeleri getir
    const members = await rolePermissionsCollection
      .find(query)
      .sort({ added_at: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    // Kullanıcı bilgilerini al
    const extendedMembers = await Promise.all(members.map(async (member) => {
      if (!usersCollection) {
        logger.error('usersCollection is not initialized');
        return { success: false, message: 'Users collection not available' };
      }
      const user = await usersCollection.findOne({ id: member.user_id });

      // role_permissions'da zaten tüm bilgiler var
      const role = {
        id: member.role_id,
        name: member.role_name,
        permissions: member.permissions
      };

      // Kullanıcı verileriyle birleştir
      return {
        id: member.id,
        user_id: member.user_id,
        team_id: member.team_id,
        role_id: member.role_id,
        role_name: member.role_name,
        permissions: member.permissions,
        status: member.status,
        added_by: member.added_by,
        added_at: member.added_at,
        user: user ? {
          id: user.id,
          name: user.name || user.displayName || user.username,
          email: user.email,
          lastLogin: user.lastLogin
        } : null,
        role
      };
    }));

    return {
      success: true,
      members: extendedMembers,
      total
    };
  } catch (error: any) {
    logger.error('Error getting team members:', error);
    return { success: false, message: 'Failed to get team members' };
  }
}

// Takım yetkilendirme kontrolü (basitleştirilmiş)
export async function authorizeTeamAction(
  userId: string,
  teamId: string,
  requiredRoles: string[]
): Promise<{
  success: boolean;
  authorized?: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    // Kullanıcının takımdaki rolünü kontrol et
    const membership = await rolePermissionsCollection.findOne({
      user_id: userId,
      team_id: teamId,
      status: 'active',
      is_system: false
    });

    if (!membership) {
      return {
        success: true,
        authorized: false,
        message: 'User is not a member of this team'
      };
    }

    // Rol kontrolü
    const hasRequiredRole = requiredRoles.includes(membership.role_id);

    return {
      success: true,
      authorized: hasRequiredRole,
      message: hasRequiredRole ? 'User is authorized' : 'User does not have required role'
    };
  } catch (error: any) {
    logger.error(`Error in authorizeTeamAction: ${error.message}`);
    return { success: false, message: 'Failed to check authorization' };
  }
}

// Takım üyeliği kontrolü
export async function checkTeamMembership(
  userId: string,
  teamId: string
): Promise<{
  success: boolean;
  isMember?: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    const membership = await rolePermissionsCollection.findOne({
      user_id: userId,
      team_id: teamId,
      status: 'active',
      is_system: false
    });

    return {
      success: true,
      isMember: !!membership
    };
  } catch (error: any) {
    logger.error(`Error checking team membership: ${error.message}`);
    return { success: false, message: 'Failed to check team membership' };
  }
}

// Takım sil
export async function deleteTeam(teamId: string): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    // Önce takım üyelerini sil
    await rolePermissionsCollection.deleteMany({
      team_id: teamId,
      is_system: false
    });

    // Takımı sil
    await teamsCollection.deleteOne({ id: teamId });

    logger.info(`Team ${teamId} deleted successfully`);

    return { success: true };
  } catch (error: any) {
    logger.error(`Error deleting team: ${error.message}`);
    return { success: false, message: 'Failed to delete team' };
  }
}

// Takım bilgilerini getir
export async function getTeamById(teamId: string): Promise<{
  success: boolean;
  team?: Team;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const team = await teamsCollection.findOne({ id: teamId });

    if (!team) {
      return { success: false, message: 'Team not found' };
    }

    return {
      success: true,
      team: team as Team
    };
  } catch (error: any) {
    logger.error(`Error getting team by ID: ${error.message}`);
    return { success: false, message: 'Failed to get team' };
  }
}

// Kullanıcının takımlarını getir (getUserTeams ile aynı)
export async function getTeamsByUserId(userId: string): Promise<{
  success: boolean;
  teams?: Team[];
  message?: string;
}> {
  return getUserTeams(userId);
}

// Takım üyesi sil
export async function removeTeamMember(
  teamId: string,
  userId: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    // Üyeyi sil
    const result = await rolePermissionsCollection.deleteOne({
      team_id: teamId,
      user_id: userId,
      is_system: false
    });

    if (result.deletedCount === 0) {
      return { success: false, message: 'Team member not found' };
    }

    // Kullanıcının teamId alanını temizle
    if (usersCollection) {
      await usersCollection.updateOne(
        { id: userId },
        { $unset: { teamId: "" } }
      );
    }

    logger.info(`Removed user ${userId} from team ${teamId}`);

    return { success: true };
  } catch (error: any) {
    logger.error(`Error removing team member: ${error.message}`);
    return { success: false, message: 'Failed to remove team member' };
  }
}

// Takım güncelle
export async function updateTeam(
  teamId: string,
  updateData: {
    name?: string;
    description?: string;
  }
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const updateObj: any = {
      updatedAt: new Date()
    };

    if (updateData.name) updateObj.name = updateData.name;
    if (updateData.description !== undefined) updateObj.description = updateData.description;

    const result = await teamsCollection.updateOne(
      { id: teamId },
      { $set: updateObj }
    );

    if (result.matchedCount === 0) {
      return { success: false, message: 'Team not found' };
    }

    logger.info(`Team ${teamId} updated successfully`);

    return { success: true };
  } catch (error: any) {
    logger.error(`Error updating team: ${error.message}`);
    return { success: false, message: 'Failed to update team' };
  }
}

// Takım üyesi güncelle
export async function updateTeamMember(
  teamId: string,
  userId: string,
  updateData: {
    roleId?: string;
  }
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    if (updateData.roleId) {
      // Sistem rolünden bilgileri al
      const systemRole = await rolePermissionsCollection.findOne({
        role_id: updateData.roleId,
        is_system: true
      });

      if (!systemRole) {
        return { success: false, message: `Role ${updateData.roleId} not found` };
      }

      // Üyeyi güncelle
      const result = await rolePermissionsCollection.updateOne(
        {
          team_id: teamId,
          user_id: userId,
          is_system: false
        },
        {
          $set: {
            role_id: updateData.roleId,
            role_name: systemRole.role_name,
            permissions: systemRole.permissions,
            updated_at: new Date()
          }
        }
      );

      if (result.matchedCount === 0) {
        return { success: false, message: 'Team member not found' };
      }
    }

    logger.info(`Updated team member ${userId} in team ${teamId}`);

    return { success: true };
  } catch (error: any) {
    logger.error(`Error updating team member: ${error.message}`);
    return { success: false, message: 'Failed to update team member' };
  }
}

// Şirketin takımlarını getir
export async function getCompanyTeams(companyId: string): Promise<{
  success: boolean;
  teams?: Team[];
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const teams = await teamsCollection
      .find({
        companyId: companyId,
        status: TeamStatus.ACTIVE
      })
      .toArray();

    return {
      success: true,
      teams: teams as Team[]
    };
  } catch (error: any) {
    logger.error(`Error getting company teams: ${error.message}`);
    return { success: false, message: 'Failed to get company teams' };
  }
}

/**
 * ✅ DUPLICATE CLEANUP: Duplicate team member kayıtlarını temizle
 * @param teamId Takım ID'si (opsiyonel - belirtilmezse tüm takımları kontrol eder)
 * @returns Temizlik sonucu
 */
export async function cleanupDuplicateTeamMembers(teamId?: string): Promise<{
  success: boolean;
  message?: string;
  removedCount?: number;
  details?: any[];
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    // Query oluştur
    const query: any = {
      is_system: false,
      user_id: { $exists: true, $ne: null }
    };
    if (teamId) {
      query.team_id = teamId;
    }

    // Tüm team member kayıtlarını al (role_permissions'dan)
    const allMembers = await rolePermissionsCollection.find(query).toArray();

    const duplicatesToRemove: any[] = [];
    const seenUserTeamPairs = new Map<string, any>();

    for (const member of allMembers) {
      const key = `${member.user_id}-${member.team_id}`;

      if (seenUserTeamPairs.has(key)) {
        // Duplicate bulundu
        const existingMember = seenUserTeamPairs.get(key);

        // Kullanıcı bilgisi olan kaydı tut, null olanı sil
        const memberUser = usersCollection ? await usersCollection.findOne({ id: member.user_id }) : null;
        const existingUser = usersCollection ? await usersCollection.findOne({ id: existingMember.user_id }) : null;

        let memberToRemove;

        if (!memberUser && existingUser) {
          // Mevcut kaydın kullanıcısı yok, yeni kaydın kullanıcısı var - mevcut kaydı sil
          memberToRemove = member;
        } else if (memberUser && !existingUser) {
          // Yeni kaydın kullanıcısı yok, mevcut kaydın kullanıcısı var - yeni kaydı sil
          memberToRemove = existingMember;
          seenUserTeamPairs.set(key, member); // Güncel kaydı sakla
        } else if (!memberUser && !existingUser) {
          // İkisi de kullanıcı bilgisi yok - daha yeni olanı tut
          if (new Date(member.added_at) > new Date(existingMember.added_at)) {
            memberToRemove = existingMember;
            seenUserTeamPairs.set(key, member);
          } else {
            memberToRemove = member;
          }
        } else {
          // İkisinde de kullanıcı var - daha yeni olanı tut (added_by='system' olanı tercih et)
          if (member.added_by === 'system' && existingMember.added_by !== 'system') {
            memberToRemove = existingMember;
            seenUserTeamPairs.set(key, member);
          } else if (existingMember.added_by === 'system' && member.added_by !== 'system') {
            memberToRemove = member;
          } else {
            // Her ikisi de aynı tipte - daha yeni olanı tut
            if (new Date(member.added_at) > new Date(existingMember.added_at)) {
              memberToRemove = existingMember;
              seenUserTeamPairs.set(key, member);
            } else {
              memberToRemove = member;
            }
          }
        }

        if (memberToRemove) {
          duplicatesToRemove.push({
            id: memberToRemove.id,
            user_id: memberToRemove.user_id,
            team_id: memberToRemove.team_id,
            added_by: memberToRemove.added_by,
            added_at: memberToRemove.added_at,
            hasUser: memberToRemove.user_id && usersCollection ? (await usersCollection.findOne({ id: memberToRemove.user_id })) !== null : false
          });
        }
      } else {
        seenUserTeamPairs.set(key, member);
      }
    }

    // Duplicate kayıtları sil
    if (duplicatesToRemove.length > 0) {
      const idsToRemove = duplicatesToRemove.map(d => d.id);
      const deleteResult = await rolePermissionsCollection.deleteMany({
        id: { $in: idsToRemove }
      });

      logger.info(`[CLEANUP] Removed ${deleteResult.deletedCount} duplicate team member records from ${teamId ? `team ${teamId}` : 'all teams'}`);

      return {
        success: true,
        message: `Removed ${deleteResult.deletedCount} duplicate team member records`,
        removedCount: deleteResult.deletedCount,
        details: duplicatesToRemove
      };
    } else {
      return {
        success: true,
        message: 'No duplicate team member records found',
        removedCount: 0,
        details: []
      };
    }
  } catch (error: any) {
    logger.error(`Error cleaning up duplicate team members: ${error.message}`);
    return {
      success: false,
      message: `Failed to cleanup duplicates: ${error.message}`
    };
  }
}
