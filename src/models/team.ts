/**
 * Team Models
 * Takım yönetimi ile ilgili veri modelleri
 */

import { ObjectId } from 'mongodb';

/**
 * Takım üyeliği durumları
 */
export enum MembershipStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  REJECTED = 'rejected'
}

/**
 * Takım durumları
 */
export enum TeamStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  SUSPENDED = 'suspended'
}

/**
 * Takım ayarları
 */
export interface TeamSettings {
  allowSelfJoin?: boolean;
  requiresAdminApproval?: boolean;
  visible?: boolean;
  publicProjectAccess?: boolean;
  memberVisibility?: 'public' | 'members_only' | 'admins_only';
  canMembersInvite?: boolean;
  visibilityLevel?: 'private' | 'public' | 'internal';
  allowExternalMembers?: boolean;
  approvalRequired?: boolean;
  [key: string]: any;
}

/**
 * Takım modeli
 */
export interface Team {
  _id?: ObjectId;
  id?: string;
  name: string;
  description?: string;
  tags?: string[];
  settings?: TeamSettings;
  createdBy: string;
  createdAt: Date;
  updatedAt?: Date;
  isArchived?: boolean;
  status?: TeamStatus;
  avatarUrl?: string;
  companyId?: string;
  memberCount?: number;
  last_active?: Date;
}

/**
 * Takım üyeliği modeli (role_permissions tablosuna uygun)
 */
export interface TeamMember {
  _id?: ObjectId;
  id: string;
  user_id: string;
  team_id: string;
  company_id: string;
  role_id: string;
  role_name: string;
  permissions: Permission[];  // Rolden gelen permissions
  status: 'active' | 'inactive';
  added_by: string;
  added_at: Date;
  created_at: Date;
  updated_at?: Date;
  is_system: false;  // User assignments are always false
}

/**
 * Rol yetkileri
 */
export interface Permission {
  resource: string;
  action: string;
}

/**
 * Rol modeli
 */
export interface Role {
  _id?: ObjectId;
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  is_system: boolean;
  created_at: Date;
  updated_at?: Date;
  team_id?: string;
  company_id?: string | null;
}

/**
 * Kullanıcı ve takım detaylarını içeren genişletilmiş takım üyeliği
 */
export interface ExtendedTeamMember extends TeamMember {
  user?: {
    id: string;
    username: string;
    displayName?: string;
    email?: string;
    avatarUrl?: string;
  };
  role?: {
    id: string;
    name: string;
    permissions?: Permission[];
  };
}

/**
 * Sayfalama seçenekleri
 */
export interface PaginationOptions {
  limit?: number;
  skip?: number;
  includeArchived?: boolean;
  includeInactive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Takım oluşturma yanıtı
 */
export interface CreateTeamResult {
  success: boolean;
  teamId?: string;
  message?: string;
}

/**
 * Takım alma yanıtı
 */
export interface GetTeamResult {
  success: boolean;
  team?: Team;
  message?: string;
}

/**
 * Birden fazla takım alma yanıtı
 */
export interface GetTeamsResult {
  success: boolean;
  teams?: Team[];
  total?: number;
  message?: string;
}

/**
 * Takım üyesi ekleme yanıtı
 */
export interface AddTeamMemberResult {
  success: boolean;
  memberId?: string;
  message?: string;
}

/**
 * Takım üyesi güncelleme/silme yanıtı
 */
export interface TeamMemberResult {
  success: boolean;
  message?: string;
}

/**
 * Takım üyelerini alma yanıtı
 */
export interface GetTeamMembersResult {
  success: boolean;
  members?: ExtendedTeamMember[];
  total?: number;
  message?: string;
}

/**
 * Takım üyeliği kontrolü yanıtı
 */
export interface TeamMembershipCheckResult {
  success: boolean;
  isMember: boolean;
  role?: string;
  status?: MembershipStatus;
  message?: string;
}

/**
 * Takım eylemi yetkilendirme yanıtı
 */
export interface TeamAuthorizationResult {
  success: boolean;
  authorized: boolean;
  message?: string;
}